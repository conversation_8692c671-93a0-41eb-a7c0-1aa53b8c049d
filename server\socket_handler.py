from flask_socketio import So<PERSON><PERSON>, emit, join_room, leave_room, disconnect
from flask import request
import random
import string
import time

class SocketHandler:
    def __init__(self, socketio, db):
        self.socketio = socketio
        self.db = db
        self.setup_handlers()
        self.active_users = {}  # 存储活跃用户 {session_id: {user_data}}

    def setup_handlers(self):
        """设置Socket.IO事件处理器"""
        @self.socketio.on('connect')
        def handle_connect():
            print(f"Client connected: {request.sid}")

        @self.socketio.on('disconnect')
        def handle_disconnect():
            print(f"Client disconnected: {request.sid}")
            if request.sid in self.active_users:
                del self.active_users[request.sid]
                self.broadcast_user_count()

        @self.socketio.on('register_user')
        def handle_register(data):
            # 确保昵称是UTF-8编码
            nickname = data.get('nickname', self.generate_nickname())
            if isinstance(nickname, bytes):
                nickname = nickname.decode('utf-8')

            ip = request.remote_addr
            session_id = request.sid

            # 先检查是否已存在该session_id的用户
            existing_user = self.db.get_user_by_session(session_id)
            if existing_user:
                # 如果存在，先删除旧用户
                self.db.remove_user(session_id)

            # 添加新用户
            user_id = self.db.add_user(nickname, ip, session_id)
            user = {
                'id': user_id,
                'nickname': nickname,
                'ip': ip,
                'session_id': session_id
            }
            self.active_users[session_id] = user

            # 获取当前比赛状态
            contest = self.db.get_active_contest()

            # 确保发送的数据是UTF-8编码
            emit('registration_success', {
                'user': user,
                'contest': contest
            })

            self.broadcast_user_count()
            self.broadcast_leaderboard()

        @self.socketio.on('typing_progress')
        def handle_typing_progress(data):
            session_id = request.sid
            user = self.db.get_user_by_session(session_id)

            if not user:
                return

            contest = self.db.get_active_contest()
            if not contest or contest['status'] != 'running':
                return

            correct_count = data.get('correct_count', 0)
            error_count = data.get('error_count', 0)
            completed = data.get('completed', False)

            self.db.update_score(user['id'], contest['id'], correct_count, error_count, completed)
            self.broadcast_leaderboard()

        @self.socketio.on('request_leaderboard')
        def handle_request_leaderboard():
            self.broadcast_leaderboard()

    def generate_nickname(self):
        """生成随机太空主题昵称"""
        # 确保昵称字符串是UTF-8编码
        prefixes = ["星际", "宇宙", "银河", "太空", "星云", "彗星", "行星", "卫星", "火箭", "飞船"]
        suffixes = ["旅行者", "探索者", "战士", "飞行员", "指挥官", "科学家", "工程师", "猎人", "守护者", "先锋"]
        nickname = f"{random.choice(prefixes)}{random.choice(suffixes)}{random.randint(1, 999)}"
        return nickname

    def broadcast_user_count(self):
        """广播当前在线用户数量"""
        user_count = len(self.active_users)
        self.socketio.emit('user_count_update', {'count': user_count})

    def broadcast_leaderboard(self):
        """广播排行榜（修复Bug 3 - 添加错误处理）"""
        try:
            contest = self.db.get_active_contest()
            if not contest:
                return

            leaderboard = self.db.get_leaderboard(contest['id'])
            if leaderboard is not None:
                self.socketio.emit('leaderboard_update', {'leaderboard': leaderboard})
            else:
                print("获取排行榜数据失败，跳过广播")
        except Exception as e:
            print(f"广播排行榜失败: {str(e)}")

    def broadcast_contest_status(self, status, contest_id=None):
        """广播比赛状态更新"""
        if not contest_id:
            contest = self.db.get_active_contest()
            if contest:
                contest_id = contest['id']
            else:
                return

        # 如果是比赛结束，添加排行榜数据
        if status == 'ended':
            leaderboard = self.db.get_leaderboard(contest_id)
            self.socketio.emit('contest_status_update', {
                'status': status,
                'contest_id': contest_id,
                'leaderboard': leaderboard  # 发送最终排行榜
            })
        else:
            self.socketio.emit('contest_status_update', {
                'status': status,
                'contest_id': contest_id
            })

    def kick_user(self, session_id):
        """踢出用户"""
        if session_id in self.active_users:
            self.db.remove_user(session_id)
            del self.active_users[session_id]
            self.socketio.emit('kicked', room=session_id)
            disconnect(session_id)
            self.broadcast_user_count()
            return True
        return False

    def broadcast_leaderboard_visibility(self, visible):
        """广播排行榜可见性设置"""
        self.socketio.emit('leaderboard_visibility', {
            'visible': visible
        })