// 初始化Socket连接
let socket;
let socketConnected = false;
let connectionTimeout;

function initializeSocket() {
    // 更新加载文本
    const loadingText = document.querySelector('.app-loading-text');
    if (loadingText) {
        loadingText.textContent = '正在连接服务器...';
    }

    // 设置连接超时
    connectionTimeout = setTimeout(() => {
        if (!socketConnected) {
            console.warn('Socket连接超时，继续加载应用');
            updateLoadingProgress(1); // 强制完成Socket加载进度
        }
    }, 5000); // 5秒超时

    // 初始化Socket
    socket = io({
        reconnectionAttempts: 3,
        timeout: 5000,
        forceNew: true
    });

    window.socket = socket;

    // 连接状态处理
    socket.on('connect', () => {
        console.log('Connected to server');
        socketConnected = true;
        clearTimeout(connectionTimeout);
        updateLoadingProgress(1); // Socket连接成功，更新进度
    });

    socket.on('connect_error', (error) => {
        console.error('Connection error:', error);
        clearTimeout(connectionTimeout);
        updateLoadingProgress(1); // 即使连接失败也更新进度，让用户可以看到界面
    });

    socket.on('disconnect', () => {
        console.log('Disconnected from server');
        showNotification('与服务器断开连接');
    });

    // 注册成功响应
    socket.on('registration_success', (data) => {
        console.log('Registration success:', data);
        const { user, contest } = data;

        // 更新用户信息
        document.getElementById('user-nickname').textContent = user.nickname;

        // 如果比赛已经开始，直接进入打字界面
        if (contest && contest.status === 'running') {
            showTypingScreen(contest.text_content);
        }
    });

    // 比赛状态更新
    socket.on('contest_status_update', (data) => {
        console.log('Contest status update:', data);
        const { status, contest_id, leaderboard } = data;

        switch (status) {
            case 'running':
                // 获取比赛内容并显示打字界面
                fetch('/api/contest/status')
                    .then(response => response.json())
                    .then(contest => {
                        if (contest && contest.text_content) {
                            showTypingScreen(contest.text_content);
                        }
                    });
                break;
            case 'paused':
                showNotification('比赛已暂停');
                disableTyping();
                break;
            case 'ended':
                showNotification('比赛已结束');

                // 检查是否有排行榜数据并更新
                if (leaderboard && Array.isArray(leaderboard)) {
                    console.log('服务器返回的排行榜数据:', leaderboard);

                    // 更新排行榜显示
                    updateLeaderboard(leaderboard, 'result-leaderboard');

                    // 查找当前用户的得分
                    const userNickname = document.getElementById('user-nickname').textContent;
                    const userResult = leaderboard.find(entry => entry.nickname === userNickname);

                    if (userResult) {
                        document.getElementById('final-score').textContent = userResult.score;
                        document.getElementById('final-nickname').textContent = userResult.nickname;
                    } else {
                        // 如果在排行榜中找不到用户，使用当前得分
                        const currentScore = parseInt(document.getElementById('score').textContent) || 0;
                        document.getElementById('final-score').textContent = currentScore;
                    }
                } else {
                    // 如果没有收到排行榜数据，使用当前得分
                    const currentScore = parseInt(document.getElementById('score').textContent) || 0;
                    document.getElementById('final-score').textContent = currentScore;
                }

                // 显示结果界面
                showResultScreen();
                break;
        }
    });

    // 在线用户数量更新
    socket.on('user_count_update', (data) => {
        document.getElementById('online-count').textContent = data.count;
    });

    // 排行榜更新
    socket.on('leaderboard_update', (data) => {
        // 更新游戏界面的排行榜
        updateLeaderboard(data.leaderboard, 'leaderboard');
        // 更新结果界面的排行榜
        updateLeaderboard(data.leaderboard, 'result-leaderboard');
    });

    // 被踢出
    socket.on('kicked', () => {
        showNotification('您已被管理员移出比赛');
        setTimeout(() => {
            window.location.reload();
        }, 2000);
    });

    // 排行榜可见性（修复Bug 2）
    socket.on('leaderboard_visibility', (data) => {
        updateLeaderboardVisibility(data.visible);
    });
}

// 统一的排行榜可见性控制函数（修复Bug 2）
function updateLeaderboardVisibility(visible) {
    const leaderboardElements = document.querySelectorAll('.leaderboard-container');
    leaderboardElements.forEach(el => {
        if (visible) {
            el.classList.remove('d-none');
        } else {
            el.classList.add('d-none');
        }
    });
    console.log(`排行榜可见性已更新: ${visible ? '显示' : '隐藏'}`);
}

// 页面加载完成后初始化Socket
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化Socket，先加载其他资源
    setTimeout(initializeSocket, 500);

    // 获取排行榜可见性设置（修复Bug 2 - 添加重试机制）
    let retryCount = 0;
    const maxRetries = 3;

    function fetchLeaderboardVisibility() {
        fetch('/api/leaderboard/visibility')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                updateLeaderboardVisibility(data.visible);
            })
            .catch(error => {
                console.error('获取排行榜可见性设置失败:', error);
                retryCount++;
                if (retryCount < maxRetries) {
                    console.log(`重试获取排行榜可见性设置 (${retryCount}/${maxRetries})`);
                    setTimeout(fetchLeaderboardVisibility, 1000 * retryCount);
                } else {
                    console.warn('达到最大重试次数，使用默认设置（显示排行榜）');
                    updateLeaderboardVisibility(true);
                }
            });
    }

    fetchLeaderboardVisibility();
});

// 辅助函数
function showTypingScreen(text) {
    // 隐藏等待界面，显示打字界面
    document.getElementById('waiting-screen').classList.add('d-none');
    document.getElementById('typing-screen').classList.remove('d-none');

    // 使用全局的TypingGame实例启动游戏
    if (window.typingGame) {
        window.typingGame.start(text);
    } else {
        console.error('打字游戏实例不存在');
        // 备用方案：直接设置文本
        document.getElementById('text-display').textContent = text;

        // 启用输入
        const typingInput = document.getElementById('typing-input');
        typingInput.disabled = false;
        typingInput.focus();
    }

    showNotification('比赛开始！');
}

function showResultScreen() {
    document.getElementById('typing-screen').classList.add('d-none');
    document.getElementById('result-screen').classList.remove('d-none');
}

function disableTyping() {
    document.getElementById('typing-input').disabled = true;
}

function showNotification(message) {
    const toast = document.getElementById('notification-toast');
    const toastMessage = document.getElementById('notification-message');
    toastMessage.textContent = message;

    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
}

function updateLeaderboard(leaderboard, elementId) {
    const leaderboardBody = document.getElementById(elementId);
    if (!leaderboardBody) return;

    leaderboardBody.innerHTML = '';

    leaderboard.forEach((entry, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${index + 1}</td>
            <td class="leaderboard-nickname">${entry.nickname}</td>
            <td>${entry.score}</td>
        `;
        leaderboardBody.appendChild(row);
    });
}