#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打字游戏Bug修复测试用例
测试三个关键bug的修复效果：
1. 翻页功能异常
2. 排行榜显示问题
3. 实时数据同步问题
"""

import unittest
import time
import json
import sys
import os

# 添加服务器路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'server'))

try:
    from models import Database
    MODELS_AVAILABLE = True
except ImportError:
    print("警告：无法导入models模块，将跳过数据库相关测试")
    MODELS_AVAILABLE = False

class TestBugFixes(unittest.TestCase):
    """Bug修复测试类"""

    def setUp(self):
        """测试前的设置"""
        if MODELS_AVAILABLE:
            self.db = Database(':memory:')  # 使用内存数据库进行测试
        else:
            self.db = None
        
    def test_bug1_pagination_state_reset(self):
        """测试Bug 1修复：翻页时状态重置"""
        print("\n=== 测试Bug 1修复：翻页功能异常 ===")
        
        # 模拟创建比赛
        contest_id = self.db.create_contest("测试文本内容，这是一个很长的文本，需要分页显示。")
        self.db.start_contest(contest_id)
        
        # 模拟用户注册
        user_id = self.db.add_user("test_user", "127.0.0.1", "session_123")
        
        # 模拟第一页输入完成
        self.db.update_score(user_id, contest_id, 10, 2, False)
        
        # 获取用户数据
        user = self.db.get_user_by_session("session_123")
        self.assertIsNotNone(user)
        self.assertEqual(user['correct_count'], 10)
        self.assertEqual(user['error_count'], 2)
        
        # 模拟翻页后继续输入
        self.db.update_score(user_id, contest_id, 15, 3, False)
        
        # 验证统计数据正确累加
        user = self.db.get_user_by_session("session_123")
        self.assertEqual(user['correct_count'], 15)
        self.assertEqual(user['error_count'], 3)
        
        print("✓ 翻页状态重置测试通过")
        
    def test_bug2_leaderboard_visibility_consistency(self):
        """测试Bug 2修复：排行榜显示一致性"""
        print("\n=== 测试Bug 2修复：排行榜显示问题 ===")
        
        # 测试设置排行榜可见性
        self.db.set_setting('leaderboard_visible', True)
        visible = self.db.get_setting('leaderboard_visible', default=False)
        self.assertTrue(visible)
        
        # 测试隐藏排行榜
        self.db.set_setting('leaderboard_visible', False)
        visible = self.db.get_setting('leaderboard_visible', default=True)
        self.assertFalse(visible)
        
        # 测试广播排行榜可见性
        self.socket_handler.broadcast_leaderboard_visibility(True)
        self.mock_socketio.emit.assert_called_with('leaderboard_visibility', {'visible': True})
        
        print("✓ 排行榜可见性一致性测试通过")
        
    def test_bug3_data_sync_error_handling(self):
        """测试Bug 3修复：实时数据同步错误处理"""
        print("\n=== 测试Bug 3修复：实时数据同步问题 ===")
        
        # 创建比赛和用户
        contest_id = self.db.create_contest("测试文本")
        self.db.start_contest(contest_id)
        user_id = self.db.add_user("test_user", "127.0.0.1", "session_123")
        
        # 测试正常的排行榜广播
        try:
            self.socket_handler.broadcast_leaderboard()
            print("✓ 正常排行榜广播测试通过")
        except Exception as e:
            self.fail(f"排行榜广播失败: {e}")
            
        # 测试异常情况下的错误处理
        with patch.object(self.db, 'get_active_contest', side_effect=Exception("数据库错误")):
            try:
                self.socket_handler.broadcast_leaderboard()
                print("✓ 异常情况错误处理测试通过")
            except Exception as e:
                self.fail(f"错误处理失败: {e}")
                
        print("✓ 实时数据同步错误处理测试通过")
        
    def test_integration_all_fixes(self):
        """集成测试：验证所有修复一起工作"""
        print("\n=== 集成测试：所有修复一起工作 ===")
        
        # 创建完整的测试场景
        contest_id = self.db.create_contest("这是一个测试文本，用于验证所有bug修复是否正常工作。")
        self.db.start_contest(contest_id)
        
        # 添加多个用户
        users = []
        for i in range(3):
            user_id = self.db.add_user(f"user_{i}", "127.0.0.1", f"session_{i}")
            users.append(user_id)
            
        # 模拟用户输入进度
        for i, user_id in enumerate(users):
            correct = (i + 1) * 10
            error = i * 2
            self.db.update_score(user_id, contest_id, correct, error, False)
            
        # 获取排行榜
        leaderboard = self.db.get_leaderboard(contest_id)
        self.assertEqual(len(leaderboard), 3)
        
        # 验证排序正确（按分数降序）
        scores = [entry['score'] for entry in leaderboard]
        self.assertEqual(scores, sorted(scores, reverse=True))
        
        # 测试排行榜广播
        self.socket_handler.broadcast_leaderboard()
        self.mock_socketio.emit.assert_called()
        
        print("✓ 集成测试通过")

def run_manual_tests():
    """运行手动测试，验证客户端功能"""
    print("\n=== 手动测试指南 ===")
    print("请按照以下步骤手动测试客户端功能：")
    print()
    print("1. 翻页功能测试：")
    print("   - 启动服务器和客户端")
    print("   - 创建一个长文本比赛")
    print("   - 输入到页面末尾，观察是否正确翻页")
    print("   - 检查输入框是否正确清空")
    print("   - 验证统计数据是否正确累加")
    print()
    print("2. 排行榜显示测试：")
    print("   - 在管理界面切换排行榜显示/隐藏")
    print("   - 检查客户端排行榜是否同步更新")
    print("   - 刷新页面，验证设置是否保持")
    print()
    print("3. 实时数据同步测试：")
    print("   - 多次快速操作管理界面")
    print("   - 观察是否有错误信息")
    print("   - 检查用户列表和排行榜是否正常更新")
    print()

if __name__ == '__main__':
    print("打字游戏Bug修复测试")
    print("=" * 50)
    
    # 运行单元测试
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 显示手动测试指南
    run_manual_tests()
